# ClipSHOOK

ClipSHOOK 是一个轻量级的 Clip Studio Paint Hook 框架，专注于提供核心的 Hook 功能。

## 项目概述

ClipSHOOK 是从原 CSPMOD_403 项目精简而来，移除了所有滤镜处理和 UI 界面相关的代码，只保留了最核心的 Hook 功能框架。

## 项目结构

```
ClipSHOOK/
├── CSPHook.cpp/h          # 核心Hook功能
├── HookTool.cpp/h         # Hook工具类
├── dllmain.cpp            # DLL入口点
├── ProjConfig.h           # 项目配置
└── framework.h/pch.cpp/h  # 预编译头文件
```

## 核心功能

### 1. Hook 框架
- **基础Hook**: 支持函数地址直接Hook
- **扩展Hook**: 支持模块+偏移量Hook
- **自动Hook**: 支持自动计算指令长度的智能Hook（需要udis86库）

### 2. Hook 工具类
- 内存保护管理
- 跳转代码生成
- 指令长度计算
- Hook状态管理

### 3. CSP 集成
- 自动检测CSP进程
- 基址计算和管理
- 延迟Hook机制

## 编译要求

### 基本要求
- Visual Studio 2019 或更高版本
- Windows SDK
- C++17 支持

### AutoHook功能（可选）
如需使用AutoHook功能，需要配置udis86库：
- 参考 `UDIS86_SETUP.md` 进行配置
- 或者注释掉AutoHook相关代码

## 使用方法

### 1. 基本Hook示例
```cpp
// 直接Hook函数地址
CatHook::Hook((void*)targetAddress, (void*)hookFunction, (void**)&originalFunction);

// Hook模块中的函数
CatHook::HookEx(L"module.dll", 0x1234, (void*)hookFunction, (void**)&originalFunction);
```

### 2. 自动Hook示例
```cpp
// 自动计算指令长度进行Hook
CatHook::AutoHook((void*)targetAddress, (void*)hookFunction, (void**)&originalFunction);
```

### 3. 延迟Hook
```cpp
// 在CSPHook::Hook_LateHook中添加需要延迟执行的Hook
void CSPHook::SetUpHook() {
    // 在这里添加你的Hook代码
}
```

## 配置说明

### 项目配置
- 目标平台：x64
- 输出类型：动态链接库(.dll)
- 字符集：Unicode

### 部署
编译后的DLL需要放置在CSP安装目录中，文件名为 `ClipSHOOK.dll`

## 注意事项

1. **兼容性**: 本框架专为Clip Studio Paint设计，可能不适用于其他应用程序
2. **版本依赖**: Hook的内存地址可能因CSP版本而异，需要相应调整
3. **安全性**: Hook操作涉及内存修改，使用时请确保备份重要数据
4. **调试**: 建议在开发环境中充分测试后再在生产环境使用

## 开发指南

### 添加新的Hook
1. 在 `CSPHook.cpp` 中添加Hook函数声明
2. 实现Hook函数逻辑
3. 在 `SetUpHook()` 中注册Hook

### 扩展功能
- 可以基于现有框架添加新的功能模块
- 建议保持代码简洁，避免重新引入复杂依赖

## 许可证

请参考原项目的许可证条款。

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 更新日志

### v1.0.0 (当前版本)
- 从CSPMOD_403精简而来
- 移除所有滤镜和UI相关代码
- 保留核心Hook功能
- 重命名为ClipSHOOK
- 简化项目结构
