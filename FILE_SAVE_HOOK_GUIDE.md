# ClipSHOOK 文件保存Hook功能指南

## 功能概述

ClipSHOOK的文件保存Hook功能可以监控和记录Clip Studio Paint的所有文件操作，包括：
- 文件创建和打开
- 文件写入操作
- 文件关闭操作
- 自动识别CSP相关文件类型

## 主要特性

### 🔍 监控功能
- **文件创建监控**：Hook CreateFileW API，记录所有文件创建/打开操作
- **写入监控**：Hook WriteFile API，记录文件写入的数据量
- **关闭监控**：Hook CloseHandle API，跟踪文件操作的完整生命周期

### 📁 智能识别
- **CSP项目文件**：.clip, .csp, .lip等项目格式
- **图像文件**：.png, .jpg, .psd, .psb等图像格式
- **路径过滤**：自动识别CSP相关路径（Documents、CELSYS等）

### ⚙️ 配置选项
- **总开关**：可完全启用/禁用文件Hook功能
- **选择性Hook**：可单独控制每个API的Hook
- **日志级别**：可选择记录所有文件或仅CSP相关文件
- **调试输出**：可控制调试信息的详细程度

## 配置说明

### 基本配置（FileSaveHook.h）

```cpp
namespace Config {
    // 主要开关
    constexpr bool ENABLE_FILE_SAVE_HOOK = true;     // 启用文件保存Hook
    constexpr bool ENABLE_DEBUG_OUTPUT = true;       // 启用调试输出
    
    // 日志选项
    constexpr bool LOG_ALL_FILE_OPERATIONS = false;  // 记录所有文件操作
    constexpr bool LOG_CSP_FILES_ONLY = true;        // 只记录CSP相关文件
    
    // API Hook选项
    constexpr bool HOOK_CreateFileW = true;          // Hook文件创建
    constexpr bool HOOK_WriteFile = true;            // Hook文件写入
    constexpr bool HOOK_CloseHandle = true;          // Hook文件关闭
}
```

### 高级配置

```cpp
// 自定义文件类型识别
constexpr const wchar_t* CSP_PROJECT_EXT[] = {
    L".clip", L".csp", L".lip", L".psd", L".psb", 
    L".png", L".jpg", L".jpeg", L".bmp", L".tiff", L".tga",
    nullptr
};

// 自定义路径关键词
constexpr const wchar_t* CSP_PATH_KEYWORDS[] = {
    L"CELSYS", L"CLIP STUDIO", L"ClipStudio", L"Documents",
    L"Pictures", L"Desktop", L"Downloads",
    nullptr
};
```

## 使用方法

### 1. 启用功能
文件保存Hook功能默认已集成到ClipSHOOK中，会在DLL加载时自动启用。

### 2. 查看日志
使用DebugView工具查看文件操作日志：

```
[ClipSHOOK] File Create/Write: C:\Users\<USER>\Documents\project.clip (Result: 0x123)
[ClipSHOOK] Writing 2048 bytes to: C:\Users\<USER>\Documents\project.clip
[ClipSHOOK] File Close: C:\Users\<USER>\Documents\project.clip (Result: 0x0)
```

### 3. 自定义配置
修改`FileSaveHook.h`中的配置选项，然后重新编译ClipSHOOK。

## 日志格式说明

### 文件创建/打开
```
[ClipSHOOK] File Create/Write: <文件路径> (Result: <句柄值>)
[ClipSHOOK] File Open/Read: <文件路径> (Result: <句柄值>)
```

### 文件写入
```
[ClipSHOOK] Writing <字节数> bytes to: <文件路径>
```

### 文件关闭
```
[ClipSHOOK] File Close: <文件路径> (Result: 0x0)
```

## 应用场景

### 🔍 调试和分析
- 分析CSP的文件保存行为
- 调试文件保存相关问题
- 监控临时文件的创建和删除

### 📊 使用统计
- 统计保存的文件类型和数量
- 分析文件保存的频率和模式
- 监控磁盘使用情况

### 🛡️ 安全监控
- 监控敏感文件的访问
- 检测异常的文件操作
- 记录文件操作审计日志

### 🔧 功能扩展
基于文件Hook可以实现：
- 自动备份保存的文件
- 文件保存时的自动压缩
- 同步文件到云存储
- 文件加密和解密

## 性能考虑

### 影响最小化
- 只Hook必要的API
- 使用高效的文件路径匹配
- 避免在Hook函数中进行复杂操作
- 使用临界区保护共享数据

### 配置建议
- **开发环境**：启用详细日志，监控所有文件
- **生产环境**：只监控CSP文件，减少日志输出
- **性能敏感**：可以禁用WriteFile Hook，只监控创建和关闭

## 故障排除

### 常见问题

**1. 日志输出过多**
- 设置 `LOG_CSP_FILES_ONLY = true`
- 禁用 `LOG_ALL_FILE_OPERATIONS`

**2. 性能影响**
- 禁用不必要的Hook（如WriteFile）
- 减少调试输出
- 优化文件路径匹配逻辑

**3. CSP功能异常**
- 检查Hook函数是否正确调用原始API
- 确保没有修改函数参数
- 验证返回值处理

### 调试方法

**1. 使用DebugView**
```
# 启动DebugView
# 启动CSP
# 执行文件保存操作
# 查看Hook日志输出
```

**2. 检查配置**
```cpp
// 临时启用详细日志
constexpr bool ENABLE_DEBUG_OUTPUT = true;
constexpr bool LOG_ALL_FILE_OPERATIONS = true;
```

**3. 验证Hook状态**
查看ClipSHOOK启动日志：
```
[ClipSHOOK] Installing file save hooks...
[ClipSHOOK] File save hooks installed successfully!
```

## 注意事项

1. **权限要求**：Hook系统API可能需要管理员权限
2. **兼容性**：确保与其他Hook工具的兼容性
3. **稳定性**：避免在Hook函数中抛出异常
4. **隐私**：注意保护用户文件路径信息
5. **性能**：大量文件操作时可能影响性能

## 扩展开发

### 添加新的文件类型
```cpp
// 在CSP_PROJECT_EXT数组中添加新扩展名
L".newext", 
```

### 添加新的Hook API
```cpp
// 1. 在Config中添加开关
constexpr bool HOOK_NewAPI = true;

// 2. 实现Hook函数
RESULT WINAPI Hook_NewAPI(PARAMS...) {
    // Hook逻辑
    return orig_NewAPI(PARAMS...);
}

// 3. 在SetupFileSaveHook中注册
CatHook::HookEx(L"kernel32.dll", "NewAPI", 
    (void*)Hook_NewAPI, (void**)&orig_NewAPI);
```

### 自定义处理逻辑
```cpp
// 在Hook函数中添加自定义逻辑
if (IsProjectFile(filePath)) {
    // 项目文件特殊处理
    CreateBackup(filePath);
}
```
