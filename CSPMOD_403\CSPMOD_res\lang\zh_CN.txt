;中文简体字符资源
;事例
;常规字符：STRID_MAINWINDOW_BUTTON_LOGIN = 登 录
;Tip字符 ：TIPID_MAINWINDOW_BUTTON_LOGIN = 点击登录
;注释符号：“;”
;使用规范：每个窗口单独成方阵，不允许出现跨窗口复用情况（一个字符资源多个窗口使用）

;语言显示名称
LANGUAGE_DISPLAY_NAME = Chinese Simplified(简体中文)

;通用
STRING_OK = 确定
STRING_CANCEL = 取消

;cef 浏览器
STRID_CEF_BROWSER_WINDOW_MODE       =    有窗模式Cef浏览器组件测试
STRID_CEF_BROWSER_BACKWARD          =    后退
STRID_CEF_BROWSER_FORWARD           =    前进
STRID_CEF_BROWSER_INPUT_ADDRESS     =    请输入地址
STRID_CEF_BROWSER_BROWSE_TO         =    转到
STRID_CEF_BROWSER_REFRESH           =    刷新
STRID_CEF_BROWSER_STOP              =    停止
STRID_CEF_BROWSER_HELLO_JS          =    你好JS！
STRID_CEF_BROWSER_INPUT_TEXT        =    输入发送给JS的文本
STRID_CEF_BROWSER_SEND_TO_JS        =    发送给JS
STRID_CEF_BROWSER_OFF_SCREEN_MODE   =    离屏渲染Cef浏览器组件测试
STRID_CEF_BROWSER_RECEIVE_JS_MSG    =    收到来自JS的消息：%s
STRID_CEF_BROWSER_MULTI_TAB         =    可拖拽多标签浏览器
STRID_CEF_BROWSER_CLOSING           =    当前窗口包含多个标签页，确定要关闭吗？

;examples/MultiLang
MULTI_LANG_WINDOW_TEXT              =    支持多语版的窗口
MULTI_LANG_SELECT_LANGUAGE          =    选择语言
MULTI_LANG_SELECT_WINDOW_MAX        =    最大化
MULTI_LANG_SELECT_WINDOW_RESTORE    =    还原
MULTI_LANG_SELECT_WINDOW_MIN        =    最小化
MULTI_LANG_SELECT_WINDOW_CLOSE      =    关闭
MULTI_LANG_LABEL_TEXT               =    这是一个支持多语言的程序。
MULTI_LANG_RICH_TEXT                =    <font color="#8B0000">这是</font>一个<bgcolor color="#6495ED">支持多语言</bgcolor>的<b>程序</b>。
