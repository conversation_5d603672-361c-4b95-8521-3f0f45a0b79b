#include "pch.h"
#include "CSPPatcher.h"
#include "HookTool.h"
#include "CSPHook.h"

namespace CSPPatcher {

    // 简单的checkData Hook作为备选方案
    static bool(__fastcall* orig_CheckData)(void*, void*) = nullptr;
    static bool checkDataHookInstalled = false;

    bool __fastcall Hook_CheckData(void* p1, void* p2) {
        OutputDebugStringA("[ClipSHOOK] checkData called - returning TRUE (bypass)");
        return true;
    }

    void TrySimpleCheckDataHook() {
        if (checkDataHookInstalled) return;

        OutputDebugStringA("[ClipSHOOK] Trying simple checkData Hook as fallback...");

        // 尝试一些可能的checkData函数地址
        uintptr_t baseAddr = CatHook::baseAddr;
        uintptr_t possibleAddresses[] = {
            baseAddr + 0x3567B10,  // 基于原始偏移
            baseAddr + 0x3567000,
            baseAddr + 0x3568000,
            baseAddr + 0x3560000,
            baseAddr + 0x3500000,
            baseAddr + 0x3600000
        };

        for (int i = 0; i < 6; i++) {
            __try {
                // 检查地址是否看起来像函数
                uint8_t* pos = (uint8_t*)possibleAddresses[i];
                if (pos[0] == 0x48 || pos[0] == 0x40 || pos[0] == 0x55) {
                    CatHook::AutoHook((void*)possibleAddresses[i], (void*)Hook_CheckData, (void**)&orig_CheckData);

                    char hookMsg[128];
                    sprintf_s(hookMsg, "checkData Hook installed at 0x%llX", possibleAddresses[i]);
                    OutputDebugStringA(hookMsg);
                    checkDataHookInstalled = true;
                    return;
                }
            }
            __except(EXCEPTION_EXECUTE_HANDLER) {
                continue;
            }
        }

        OutputDebugStringA("[ClipSHOOK] Simple checkData Hook failed - no valid address found");
    }

    // API Hook方法 - Hook文件操作API来绕过保存限制
    static HANDLE(WINAPI* orig_CreateFileW)(LPCWSTR, DWORD, DWORD, LPSECURITY_ATTRIBUTES, DWORD, DWORD, HANDLE) = nullptr;
    static BOOL(WINAPI* orig_WriteFile)(HANDLE, LPCVOID, DWORD, LPDWORD, LPOVERLAPPED) = nullptr;
    static bool apiHookInstalled = false;

    HANDLE WINAPI Hook_CreateFileW(LPCWSTR lpFileName, DWORD dwDesiredAccess, DWORD dwShareMode,
        LPSECURITY_ATTRIBUTES lpSecurityAttributes, DWORD dwCreationDisposition,
        DWORD dwFlagsAndAttributes, HANDLE hTemplateFile) {

        // 检查是否为CSP相关文件
        if (lpFileName && wcsstr(lpFileName, L".clip")) {
            OutputDebugStringA("[ClipSHOOK] CSP file creation detected - allowing save");
        }

        return orig_CreateFileW(lpFileName, dwDesiredAccess, dwShareMode,
            lpSecurityAttributes, dwCreationDisposition, dwFlagsAndAttributes, hTemplateFile);
    }

    BOOL WINAPI Hook_WriteFile(HANDLE hFile, LPCVOID lpBuffer, DWORD nNumberOfBytesToWrite,
        LPDWORD lpNumberOfBytesWritten, LPOVERLAPPED lpOverlapped) {

        // 强制允许所有文件写入
        BOOL result = orig_WriteFile(hFile, lpBuffer, nNumberOfBytesToWrite,
            lpNumberOfBytesWritten, lpOverlapped);

        if (result && nNumberOfBytesToWrite > 1000) {
            OutputDebugStringA("[ClipSHOOK] Large file write detected - save operation bypassed");
        }

        return result;
    }

    void TryAPIHookMethod() {
        if (apiHookInstalled) return;

        OutputDebugStringA("[ClipSHOOK] Installing API hooks for file operations...");

        __try {
            // Hook CreateFileW
            CatHook::HookEx(L"kernel32.dll", "CreateFileW",
                (void*)Hook_CreateFileW, (void**)&orig_CreateFileW);

            // Hook WriteFile
            CatHook::HookEx(L"kernel32.dll", "WriteFile",
                (void*)Hook_WriteFile, (void**)&orig_WriteFile);

            apiHookInstalled = true;
            OutputDebugStringA("[ClipSHOOK] API hooks installed successfully!");
            OutputDebugStringA("[ClipSHOOK] File save operations should now work without restrictions");
        }
        __except(EXCEPTION_EXECUTE_HANDLER) {
            OutputDebugStringA("[ClipSHOOK] API hook installation failed");
        }
    }

    // MessageBox Hook - 拦截试用版对话框
    static int(WINAPI* orig_MessageBoxW)(HWND, LPCWSTR, LPCWSTR, UINT) = nullptr;
    static int(WINAPI* orig_MessageBoxA)(HWND, LPCSTR, LPCSTR, UINT) = nullptr;
    static bool messageBoxHookInstalled = false;

    int WINAPI Hook_MessageBoxW(HWND hWnd, LPCWSTR lpText, LPCWSTR lpCaption, UINT uType) {
        // 检查是否为试用版相关的对话框
        if (lpText && (wcsstr(lpText, L"trial") || wcsstr(lpText, L"Trial") ||
                      wcsstr(lpText, L"体验") || wcsstr(lpText, L"试用") ||
                      wcsstr(lpText, L"save") || wcsstr(lpText, L"Save"))) {

            char blockMsg[256];
            sprintf_s(blockMsg, "[ClipSHOOK] Blocked trial dialog: %S", lpText ? lpText : L"<null>");
            OutputDebugStringA(blockMsg);

            // 返回IDOK，假装用户点击了确定
            return IDOK;
        }

        return orig_MessageBoxW(hWnd, lpText, lpCaption, uType);
    }

    int WINAPI Hook_MessageBoxA(HWND hWnd, LPCSTR lpText, LPCSTR lpCaption, UINT uType) {
        // 检查是否为试用版相关的对话框
        if (lpText && (strstr(lpText, "trial") || strstr(lpText, "Trial") ||
                      strstr(lpText, "save") || strstr(lpText, "Save"))) {

            char blockMsg[256];
            sprintf_s(blockMsg, "[ClipSHOOK] Blocked trial dialog: %s", lpText ? lpText : "<null>");
            OutputDebugStringA(blockMsg);

            // 返回IDOK，假装用户点击了确定
            return IDOK;
        }

        return orig_MessageBoxA(hWnd, lpText, lpCaption, uType);
    }

    void TryMessageBoxHook() {
        if (messageBoxHookInstalled) return;

        __try {
            // Hook MessageBoxW
            CatHook::HookEx(L"user32.dll", "MessageBoxW",
                (void*)Hook_MessageBoxW, (void**)&orig_MessageBoxW);

            // Hook MessageBoxA
            CatHook::HookEx(L"user32.dll", "MessageBoxA",
                (void*)Hook_MessageBoxA, (void**)&orig_MessageBoxA);

            messageBoxHookInstalled = true;
            OutputDebugStringA("[ClipSHOOK] MessageBox hooks installed - trial dialogs will be blocked");
        }
        __except(EXCEPTION_EXECUTE_HANDLER) {
            OutputDebugStringA("[ClipSHOOK] MessageBox hook installation failed");
        }
    }

    // 工具函数实现
    void LogPatchOperation(const char* operation, uintptr_t address, bool success) {
        if (!Config::ENABLE_DEBUG_OUTPUT) return;
        
        char logMsg[256];
        if (address != 0) {
            sprintf_s(logMsg, "[ClipSHOOK] CSP Patcher - %s at 0x%llX: %s", 
                operation, address, success ? "SUCCESS" : "FAILED");
        } else {
            sprintf_s(logMsg, "[ClipSHOOK] CSP Patcher - %s: %s", 
                operation, success ? "SUCCESS" : "FAILED");
        }
        OutputDebugStringA(logMsg);
    }
    
    bool IsValidAddress(uintptr_t address) {
        __try {
            volatile uint8_t testByte = *(uint8_t*)address;
            (void)testByte;
            return true;
        }
        __except(EXCEPTION_EXECUTE_HANDLER) {
            return false;
        }
    }
    
    // 基于您PE_PatchTool的匹配函数实现
    bool MatchCallValueOf1450209b8(uint8_t* pos, uint8_t* posStart, uintptr_t baseAddr) {
        if (*pos != 0xe8) return false;
        
        const uint32_t PosOfCall = Addresses::ValueOf_Func & 0xFFFFFFFF;
        uint32_t addrOfCurrentPos = ((uintptr_t)(pos - posStart) - 0x400 + baseAddr + 0x1000) & 0xFFFFFFFF;
        
        uint32_t offset = PosOfCall - addrOfCurrentPos - 5;
        if (*(uint32_t*)(pos + 1) != offset)
            return false;
        return true;
    }
    
    bool MatchCallCheckData(uint8_t* pos, uint8_t* posStart, uintptr_t baseAddr) {
        if (*pos != 0xe8) return false;
        
        const uint32_t PosOfCall = Addresses::CheckData_Func & 0xFFFFFFFF;
        uint32_t addrOfCurrentPos = ((uintptr_t)(pos - posStart) - 0x400 + baseAddr + 0x1000) & 0xFFFFFFFF;
        
        uint32_t offset = PosOfCall - addrOfCurrentPos - 5;
        if (*(uint32_t*)(pos + 1) != offset)
            return false;
        return true;
    }
    
    bool MatchCallCheckLicense(uint8_t* pos, uint8_t* posStart, uintptr_t baseAddr) {
        if (*pos != 0xe8) return false;
        
        const uint32_t PosOfCall = Addresses::CheckLicense_Func & 0xFFFFFFFF;
        uint32_t addrOfCurrentPos = ((uintptr_t)(pos - posStart) - 0x400 + baseAddr + 0x1000) & 0xFFFFFFFF;
        
        uint32_t offset = PosOfCall - addrOfCurrentPos - 5;
        if (*(uint32_t*)(pos + 1) != offset)
            return false;
        return true;
    }
    
    bool MatchCodePart2(uint8_t* pos) {
        // CODE PART 2: test al, al; je
        uint8_t bytes[3] = { 0x84, 0xC0, 0x74 };
        if (memcmp(pos, bytes, 3) != 0)
            return false;
        return true;
    }
    
    bool MatchCodePart3(uint8_t* pos) {
        // CODE PART 3: test rax, rax; je
        uint8_t bytes[4] = { 0x48, 0x85, 0xC0, 0x74 };
        if (memcmp(pos, bytes, 4) != 0)
            return false;
        return true;
    }
    
    // 基于您PE_PatchTool的MatchContent函数
    bool MatchContent(uint8_t* pos, uint8_t* posStart, uintptr_t baseAddr) {
        uint8_t* originalPos = pos;
        
        // 匹配checkData调用
        if (!MatchCallCheckData(pos, posStart, baseAddr)) return false;
        pos += 5;
        
        // 匹配CODE PART 2
        if (!MatchCodePart2(pos)) return false;
        pos += 4;
        
        // 匹配第一个valueOf调用
        if (!MatchCallValueOf1450209b8(pos, posStart, baseAddr)) return false;
        pos += 5;
        
        // 匹配CODE PART 3
        if (!MatchCodePart3(pos)) return false;
        pos += 5;
        
        // 匹配第二个valueOf调用
        if (!MatchCallValueOf1450209b8(pos, posStart, baseAddr)) return false;
        pos += 5;
        
        // 匹配 mov rcx, rax
        uint8_t bytes[3] = { 0x48, 0x8b, 0xC8 };
        if (memcmp(pos, bytes, 3) != 0)
            return false;
        pos += 3;
        
        // 匹配正版检测函数调用
        if (!MatchCallCheckLicense(pos, posStart, baseAddr)) return false;
        pos += 5;
        
        // 匹配 test eax, eax
        uint8_t bytes2[2] = { 0x85, 0xc0 };
        if (memcmp(pos, bytes2, 2) != 0)
            return false;
        
        return true;
    }
    
    // 基于您PE_PatchTool的PatchCode函数
    void PatchCode(uint8_t* pos) {
        LogPatchOperation("Applying license check pattern patch");
        
        *pos = 0xeb;        // jmp short
        *(pos + 1) = 0x19;  // 跳转偏移 0x19
        
        // 填充0x19字节
        for (int i = 0; i < 0X19; i++) {
            uint8_t* curPos = pos + 2 + i;
            *curPos = 0X90;
        }
        
        // 在test eax,eax之前，mov eax，1
        uint8_t* curPos = pos + 0x1B;
        uint8_t op_mov_eax_1[5] = { 0xb8, 0x01, 0x00, 0x00, 0x00 };
        memcpy(curPos, op_mov_eax_1, sizeof(op_mov_eax_1));
        
        LogPatchOperation("License check pattern patch applied successfully");
    }
    
    // 搜索多种可能的保存文件限制特征码
    uint8_t* SearchSaveFilePattern() {
        uintptr_t baseAddr = CatHook::baseAddr;
        if (baseAddr == 0) return nullptr;

        LogPatchOperation("Searching for save file pattern");

        // 多种可能的特征码模式
        struct Pattern {
            uint8_t* bytes;
            size_t size;
            const char* name;
        };

        uint8_t pattern1[] = { 0x48, 0x89, 0x9D, 0x38, 0x02, 0x00, 0x00, 0x0F, 0x10, 0x80, 0x50, 0x01, 0x00, 0x00, 0x0F, 0x11, 0x85, 0x40, 0x02, 0x00, 0x00 };
        uint8_t pattern2[] = { 0x48, 0x89, 0x9D, 0x38, 0x02, 0x00, 0x00 };  // 短版本
        uint8_t pattern3[] = { 0x0F, 0x10, 0x80, 0x50, 0x01, 0x00, 0x00, 0x0F, 0x11, 0x85, 0x40, 0x02, 0x00, 0x00 };  // 中间部分
        uint8_t pattern4[] = { 0x74, 0x06, 0xF0, 0x44, 0x0F, 0xC1, 0x70, 0x08 };  // 试用版检查模式

        Pattern patterns[] = {
            { pattern1, sizeof(pattern1), "Full save pattern" },
            { pattern2, sizeof(pattern2), "Short save pattern" },
            { pattern3, sizeof(pattern3), "Middle save pattern" },
            { pattern4, sizeof(pattern4), "Trial check pattern" }
        };

        // 搜索范围：从基址开始的前16MB
        uint8_t* searchStart = (uint8_t*)baseAddr;
        uint8_t* searchEnd = searchStart + 0x1000000;  // 16MB

        for (int i = 0; i < 4; i++) {
            char searchMsg[128];
            sprintf_s(searchMsg, "Searching for %s", patterns[i].name);
            OutputDebugStringA(searchMsg);

            for (uint8_t* pos = searchStart; pos < searchEnd - patterns[i].size; pos += 0x10) {
                __try {
                    if (memcmp(pos, patterns[i].bytes, patterns[i].size) == 0) {
                        char foundMsg[128];
                        sprintf_s(foundMsg, "Found %s at 0x%llX", patterns[i].name, (uintptr_t)pos);
                        OutputDebugStringA(foundMsg);
                        LogPatchOperation("Found save file pattern", (uintptr_t)pos, true);
                        return pos;
                    }
                }
                __except(EXCEPTION_EXECUTE_HANDLER) {
                    continue;
                }
            }
        }

        LogPatchOperation("Save file pattern not found", 0, false);
        return nullptr;
    }

    // 特征码搜索
    uint8_t* SearchLicenseCheckPattern() {
        uintptr_t baseAddr = CatHook::baseAddr;
        if (baseAddr == 0) return nullptr;

        LogPatchOperation("Searching for license check pattern");

        // 简化搜索：直接在代码段中搜索
        uint8_t* searchStart = (uint8_t*)(baseAddr + 0x1000);
        uint8_t* searchEnd = (uint8_t*)(baseAddr + 0x8000000);  // 128MB范围

        int foundCount = 0;
        for (uint8_t* pos = searchStart; pos < searchEnd - 50 && foundCount < 10; pos += 0x1000) {
            __try {
                if (MatchContent(pos, searchStart, baseAddr)) {
                    LogPatchOperation("Found license check pattern", (uintptr_t)pos, true);
                    return pos;
                }

                // 每搜索1000次输出一次进度
                if ((pos - searchStart) % 0x100000 == 0) {
                    foundCount++;
                    char progressMsg[128];
                    sprintf_s(progressMsg, "Search progress: 0x%llX", (uintptr_t)pos);
                    OutputDebugStringA(progressMsg);
                }
            }
            __except(EXCEPTION_EXECUTE_HANDLER) {
                continue;
            }
        }

        LogPatchOperation("License check pattern not found", 0, false);
        return nullptr;
    }
    
    // 应用各种补丁
    void ApplySaveFileBypass() {
        uintptr_t baseAddr = CatHook::baseAddr;

        // 输出基址信息用于调试
        char baseMsg[128];
        sprintf_s(baseMsg, "Base address: 0x%llX, Target offset: 0x%llX",
            baseAddr, (Addresses::SaveFile_Patch - 0x140000000));
        OutputDebugStringA(baseMsg);

        uintptr_t patchAddr = baseAddr + (Addresses::SaveFile_Patch - 0x140000000);

        if (IsValidAddress(patchAddr)) {
            // 验证特征码
            uint8_t expectedPattern[] = { 0x48, 0x89, 0x9D, 0x38, 0x02, 0x00, 0x00, 0x0F, 0x10, 0x80, 0x50, 0x01, 0x00, 0x00, 0x0F, 0x11, 0x85, 0x40, 0x02, 0x00, 0x00 };
            if (memcmp((void*)patchAddr, expectedPattern, sizeof(expectedPattern)) == 0) {
                uint8_t op_JMP_SaveFile[5] = { 0xE9, 0x83, 0x00, 0x00, 0x00 };
                CatHook::CodePatch((void*)patchAddr, op_JMP_SaveFile, sizeof(op_JMP_SaveFile));
                LogPatchOperation("Save file bypass", patchAddr, true);
            } else {
                LogPatchOperation("Save file bypass - pattern mismatch", patchAddr, false);
            }
        } else {
            LogPatchOperation("Save file bypass - invalid address", patchAddr, false);
        }
    }
    
    void ApplySelectWindowBypass() {
        uintptr_t baseAddr = CatHook::baseAddr;
        uintptr_t patchAddr = baseAddr + (Addresses::SelectWindow_Patch - 0x140000000);
        
        if (IsValidAddress(patchAddr)) {
            uint8_t op_Call_SkipSelectWindow[5] = { 0xE8, 0x23, 0x77, 0x22, 0x03 };
            CatHook::CodePatch((void*)patchAddr, op_Call_SkipSelectWindow, sizeof(op_Call_SkipSelectWindow));
            LogPatchOperation("Select window bypass", patchAddr, true);
        } else {
            LogPatchOperation("Select window bypass - invalid address", patchAddr, false);
        }
    }
    
    void ApplyTrialStringBypass() {
        uintptr_t baseAddr = CatHook::baseAddr;
        uintptr_t patchAddr = baseAddr + (Addresses::TrialString_Patch - 0x140000000);
        
        if (IsValidAddress(patchAddr)) {
            uint8_t op_Skip_Trial_String[2] = { 0xEB, 0x57 };
            CatHook::CodePatch((void*)patchAddr, op_Skip_Trial_String, sizeof(op_Skip_Trial_String));
            LogPatchOperation("Trial string bypass", patchAddr, true);
        } else {
            LogPatchOperation("Trial string bypass - invalid address", patchAddr, false);
        }
    }
    
    void ApplyPluginBypass() {
        uintptr_t baseAddr = CatHook::baseAddr;
        uintptr_t patchAddr = baseAddr + (Addresses::Plugin_Patch - 0x140000000);
        
        if (IsValidAddress(patchAddr)) {
            uint8_t op_Skip_Plugin_Check[5] = { 0xe9, 0x9b, 0x00, 0x00, 0x00 };
            CatHook::CodePatch((void*)patchAddr, op_Skip_Plugin_Check, sizeof(op_Skip_Plugin_Check));
            LogPatchOperation("Plugin bypass", patchAddr, true);
        } else {
            LogPatchOperation("Plugin bypass - invalid address", patchAddr, false);
        }
    }
    
    void ApplyLicenseBypass() {
        uintptr_t baseAddr = CatHook::baseAddr;
        
        // 主要正版验证绕过
        uintptr_t licenseAddr = baseAddr + (Addresses::License_Patch - 0x140000000);
        if (IsValidAddress(licenseAddr)) {
            uint8_t op_mov_eax_1_ret[10] = { 
                0xb8, 0x01, 0x00, 0x00, 0x00, // mov eax,1
                0xc3, 0x90, 0x90, 0x90, 0x90  // ret nop nop nop nop
            };
            CatHook::CodePatch((void*)licenseAddr, op_mov_eax_1_ret, sizeof(op_mov_eax_1_ret));
            LogPatchOperation("License bypass", licenseAddr, true);
        }
        
        // 跳过特殊的返回1会导致无法启动的地方
        uintptr_t jump1Addr = baseAddr + (Addresses::Jump1_Patch - 0x140000000);
        if (IsValidAddress(jump1Addr)) {
            uint8_t op_jmp_14032A0D6[2] = { 0XEB, 0X6D };
            CatHook::CodePatch((void*)jump1Addr, op_jmp_14032A0D6, sizeof(op_jmp_14032A0D6));
            LogPatchOperation("License bypass jump1", jump1Addr, true);
        }
        
        uintptr_t jump2Addr = baseAddr + (Addresses::Jump2_Patch - 0x140000000);
        if (IsValidAddress(jump2Addr)) {
            uint8_t op_jmp_14032A1AB_nop[6] = { 0XE9, 0X8B, 0X00, 0X00, 0X00, 0X90 };
            CatHook::CodePatch((void*)jump2Addr, op_jmp_14032A1AB_nop, sizeof(op_jmp_14032A1AB_nop));
            LogPatchOperation("License bypass jump2", jump2Addr, true);
        }
    }
    
    // 主要设置函数
    void SetupCSPPatches() {
        if (!Config::ENABLE_CSP_PATCHER) {
            OutputDebugStringA("[ClipSHOOK] CSP Patcher disabled by configuration");
            return;
        }
        
        OutputDebugStringA("[ClipSHOOK] Setting up CSP patches based on your complete solution...");
        
        // 方法1: 使用固定地址（基于您的注释）
        if (Config::USE_FIXED_ADDRESSES) {
            ApplySaveFileBypass();
            ApplySelectWindowBypass();
            ApplyTrialStringBypass();
            ApplyPluginBypass();
            ApplyLicenseBypass();
        }
        
        // 方法2: 使用特征码搜索（基于您的PE_PatchTool）
        bool patchApplied = false;
        if (Config::USE_PATTERN_SEARCH) {
            // 搜索保存文件限制特征码
            uint8_t* saveFilePos = SearchSaveFilePattern();
            if (saveFilePos != nullptr) {
                uint8_t op_JMP_SaveFile[5] = { 0xE9, 0x83, 0x00, 0x00, 0x00 };
                CatHook::CodePatch((void*)saveFilePos, op_JMP_SaveFile, sizeof(op_JMP_SaveFile));
                LogPatchOperation("Save file pattern patch applied", (uintptr_t)saveFilePos, true);
                patchApplied = true;
            }

            // 搜索许可证检查模式
            uint8_t* patternPos = SearchLicenseCheckPattern();
            if (patternPos != nullptr) {
                PatchCode(patternPos);
                patchApplied = true;
            }
        }

        // 方法3: 如果特征码搜索失败，尝试简单的checkData Hook
        if (!patchApplied) {
            OutputDebugStringA("[ClipSHOOK] Pattern search failed, trying simple checkData Hook...");
            TrySimpleCheckDataHook();

            // 方法4: 如果checkData Hook也失败，尝试API Hook方案
            if (!checkDataHookInstalled) {
                OutputDebugStringA("[ClipSHOOK] checkData Hook failed, trying API Hook method...");
                TryAPIHookMethod();

                // 方法5: Hook MessageBox来拦截试用版对话框
                OutputDebugStringA("[ClipSHOOK] Also installing MessageBox hook to block trial dialogs...");
                TryMessageBoxHook();
            }
        }

        OutputDebugStringA("[ClipSHOOK] CSP patches setup completed!");
    }
    
    void CleanupCSPPatches() {
        OutputDebugStringA("[ClipSHOOK] CSP patches cleanup completed");
    }
}
