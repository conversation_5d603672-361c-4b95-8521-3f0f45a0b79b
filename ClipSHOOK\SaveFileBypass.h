#pragma once

//=============================================================================
// ClipSHOOK 保存文件绕过功能
// 基于PE_PatchTool的逻辑实现动态内存补丁
//=============================================================================

#include <windows.h>

namespace SaveFileBypass {
    
    // 配置选项
    namespace Config {
        // 是否启用保存文件绕过
        constexpr bool ENABLE_SAVE_FILE_BYPASS = true;
        
        // 是否启用调试输出
        constexpr bool ENABLE_DEBUG_OUTPUT = true;
        
        // 绕过方式选择
        constexpr bool USE_PATTERN_SEARCH = true;     // 使用特征码搜索（推荐）
        constexpr bool USE_FIXED_ADDRESS = false;     // 使用固定地址
        constexpr bool USE_CHECKDATA_HOOK = true;     // Hook checkData函数
        
        // 搜索范围配置
        constexpr size_t SEARCH_START_OFFSET = 0x1000;      // 搜索起始偏移
        constexpr size_t SEARCH_END_OFFSET = 0x5000000;     // 搜索结束偏移
    }
    
    // 基于PE_PatchTool的特征码定义
    namespace Patterns {
        // CODE PART 2: test al, al; je
        extern const uint8_t CodePart2_bytes[3];
        constexpr size_t CodePart2_size = 3;

        // CODE PART 3: test rax, rax; je
        extern const uint8_t CodePart3_bytes[4];
        constexpr size_t CodePart3_size = 4;

        // mov rcx, rax
        extern const uint8_t MovRcxRax_bytes[3];
        constexpr size_t MovRcxRax_size = 3;

        // test eax, eax
        extern const uint8_t TestEaxEax_bytes[2];
        constexpr size_t TestEaxEax_size = 2;

        // 保存文件限制的特征码（来自注释）
        extern const uint8_t SaveFile_bytes[21];
        constexpr size_t SaveFile_size = 21;
    }
    
    // 地址信息（基于注释中的地址）
    namespace Addresses {
        constexpr uintptr_t CheckData_Func = 0x143567B10;      // checkData函数
        constexpr uintptr_t ValueOf_Func = 0x1434da5B0;        // valueOf(1450209B8)函数
        constexpr uintptr_t CheckLicense_Func = 0x1434DDDD0;   // 重要正版检测函数
        constexpr uintptr_t SaveFile_Patch = 0x14049CC80;      // 保存文件限制地址
    }
    
    // 主要功能函数
    void SetupSaveFileBypass();
    void CleanupSaveFileBypass();
    
    // 特征码搜索和补丁功能
    bool SearchAndPatchSaveRestriction();
    bool SearchLicenseCheckPattern();
    uintptr_t FindLicenseCheckSequence();
    
    // 基于PE_PatchTool的匹配函数
    bool MatchCallCheckData(uint8_t* pos, uintptr_t baseAddr);
    bool MatchCallValueOf(uint8_t* pos, uintptr_t baseAddr);
    bool MatchCallCheckLicense(uint8_t* pos, uintptr_t baseAddr);
    bool MatchCodePart2(uint8_t* pos);
    bool MatchCodePart3(uint8_t* pos);
    bool MatchCompletePattern(uint8_t* pos, uintptr_t baseAddr);
    
    // 补丁应用函数
    void ApplyLicenseBypassPatch(uint8_t* pos);
    void ApplySaveFilePatch(uintptr_t address);
    
    // checkData Hook功能
    bool __fastcall Hook_CheckData(void* p1, void* p2);
    void SetupCheckDataHook();
    
    // 工具函数
    bool IsValidAddress(uintptr_t address);
    void LogBypassOperation(const char* operation, bool success = true);
    uintptr_t SearchMemoryPattern(uintptr_t startAddr, size_t searchSize,
        const uint8_t* pattern, size_t patternSize);

    // 计算相对地址的工具函数
    uint32_t CalculateRelativeAddress(uintptr_t from, uintptr_t to);
    bool IsCallToFunction(uint8_t* callPos, uintptr_t targetFunc, uintptr_t baseAddr);
}

//=============================================================================
// 实现说明
//=============================================================================
/*
保存文件绕过实现说明：

1. 基于PE_PatchTool的逻辑：
   - 搜索特定的代码序列模式
   - 匹配checkData调用、valueOf调用、正版检测函数调用
   - 在找到的位置应用补丁

2. 补丁策略（来自PE_PatchTool）：
   - 使用短跳转(0xEB)跳过验证逻辑
   - 填充NOP指令(0x90)
   - 在test eax,eax之前插入mov eax,1

3. checkData Hook：
   - 直接Hook checkData函数强制返回true
   - 这是最直接的绕过方式

4. 双重保险：
   - 既使用代码补丁又使用函数Hook
   - 确保各种情况下都能绕过限制

使用优先级：
1. 优先使用特征码搜索（更安全，适应性强）
2. 备选使用固定地址（需要版本匹配）
3. 同时启用checkData Hook作为补充
*/
