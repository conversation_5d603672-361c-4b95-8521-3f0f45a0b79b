#include "pch.h"
#include "SimpleBypass.h"
#include "HookTool.h"
#include "CSPHook.h"

namespace SimpleBypass {
    
    // 定义特征码数组
    namespace Patterns {
        // 特征码1: 48 89 5C 24 08 57 48 83 EC 20 (常见函数开头)
        const uint8_t Pattern1_bytes[10] = { 0x48, 0x89, 0x5C, 0x24, 0x08, 0x57, 0x48, 0x83, 0xEC, 0x20 };
        
        // 特征码2: 40 53 48 83 EC 20 48 8B D9 (另一种模式)
        const uint8_t Pattern2_bytes[8] = { 0x40, 0x53, 0x48, 0x83, 0xEC, 0x20, 0x48, 0x8B };
        
        // 特征码3: 48 89 5C 24 10 48 89 (简单模式)
        const uint8_t Pattern3_bytes[6] = { 0x48, 0x89, 0x5C, 0x24, 0x10, 0x48 };
    }
    
    // 原始函数指针
    static bool(__fastcall* orig_CheckData)(void*, void*) = nullptr;
    static bool hookInstalled = false;
    
    // 工具函数实现
    void LogBypassOperation(const char* operation, uintptr_t address, bool success) {
        if (!Config::ENABLE_DEBUG_OUTPUT) return;
        
        char logMsg[256];
        if (address != 0) {
            sprintf_s(logMsg, "[ClipSHOOK] Simple Bypass - %s at 0x%llX: %s", 
                operation, address, success ? "SUCCESS" : "FAILED");
        } else {
            sprintf_s(logMsg, "[ClipSHOOK] Simple Bypass - %s: %s", 
                operation, success ? "SUCCESS" : "FAILED");
        }
        OutputDebugStringA(logMsg);
    }
    
    bool IsValidFunctionAddress(uintptr_t address) {
        __try {
            // 检查地址是否可读
            volatile uint8_t testByte = *(uint8_t*)address;
            
            // 检查是否看起来像函数开头
            uint8_t* pos = (uint8_t*)address;
            
            // 常见的函数开头字节
            if (pos[0] == 0x48 || pos[0] == 0x40 || pos[0] == 0x55 || pos[0] == 0x56 || pos[0] == 0x57) {
                return true;
            }
            
            return false;
        }
        __except(EXCEPTION_EXECUTE_HANDLER) {
            return false;
        }
    }
    
    // checkData Hook实现
    bool __fastcall Hook_CheckData(void* p1, void* p2) {
        // 强制返回true，绕过所有验证
        if (Config::ENABLE_DEBUG_OUTPUT) {
            OutputDebugStringA("[ClipSHOOK] checkData called - returning TRUE (simple bypass)");
        }
        return true;
    }
    
    // 尝试在指定地址Hook
    bool TryHookAtAddress(uintptr_t address) {
        if (!IsValidFunctionAddress(address)) {
            LogBypassOperation("Invalid function address", address, false);
            return false;
        }
        
        __try {
            CatHook::AutoHook((void*)address, (void*)Hook_CheckData, (void**)&orig_CheckData);
            LogBypassOperation("checkData Hook installed", address, true);
            return true;
        }
        __except(EXCEPTION_EXECUTE_HANDLER) {
            LogBypassOperation("Hook installation failed", address, false);
            return false;
        }
    }
    
    // 特征码搜索
    uintptr_t SearchByPattern(const uint8_t* pattern, size_t patternSize) {
        uintptr_t baseAddr = CatHook::baseAddr;
        if (baseAddr == 0) return 0;
        
        LogBypassOperation("Starting pattern search");
        
        for (uintptr_t offset = 0x1000; offset < Config::SEARCH_RANGE; offset += 0x1000) {
            uintptr_t searchAddr = baseAddr + offset;
            
            __try {
                uint8_t* pos = (uint8_t*)searchAddr;
                if (memcmp(pos, pattern, patternSize) == 0) {
                    LogBypassOperation("Pattern found", searchAddr, true);
                    return searchAddr;
                }
            }
            __except(EXCEPTION_EXECUTE_HANDLER) {
                // 继续搜索
                continue;
            }
        }
        
        LogBypassOperation("Pattern search failed", 0, false);
        return 0;
    }
    
    // 查找checkData地址
    uintptr_t FindCheckDataAddress() {
        uintptr_t baseAddr = CatHook::baseAddr;
        if (baseAddr == 0) {
            LogBypassOperation("Invalid base address", 0, false);
            return 0;
        }
        
        // 方法1: 尝试已知地址
        uintptr_t knownAddresses[] = {
            KnownAddresses::CheckData_V1,
            KnownAddresses::CheckData_V2,
            KnownAddresses::CheckData_V3,
            KnownAddresses::CheckData_V4
        };
        
        for (int i = 0; i < 4; i++) {
            if (IsValidFunctionAddress(knownAddresses[i])) {
                LogBypassOperation("Found valid known address", knownAddresses[i], true);
                return knownAddresses[i];
            }
        }
        
        // 方法2: 特征码搜索
        uintptr_t result = SearchByPattern(Patterns::Pattern1_bytes, Patterns::Pattern1_size);
        if (result != 0) return result;
        
        result = SearchByPattern(Patterns::Pattern2_bytes, Patterns::Pattern2_size);
        if (result != 0) return result;
        
        result = SearchByPattern(Patterns::Pattern3_bytes, Patterns::Pattern3_size);
        if (result != 0) return result;
        
        LogBypassOperation("All search methods failed", 0, false);
        return 0;
    }
    
    // 测试所有已知地址
    void TestAllKnownAddresses() {
        LogBypassOperation("Testing all known addresses");
        
        uintptr_t addresses[] = {
            KnownAddresses::CheckData_V1,
            KnownAddresses::CheckData_V2,
            KnownAddresses::CheckData_V3,
            KnownAddresses::CheckData_V4
        };
        
        for (int i = 0; i < 4; i++) {
            bool valid = IsValidFunctionAddress(addresses[i]);
            LogBypassOperation("Address test", addresses[i], valid);
        }
    }
    
    // 测试特征码搜索
    void TestPatternSearch() {
        LogBypassOperation("Testing pattern search");
        
        uintptr_t result1 = SearchByPattern(Patterns::Pattern1_bytes, Patterns::Pattern1_size);
        uintptr_t result2 = SearchByPattern(Patterns::Pattern2_bytes, Patterns::Pattern2_size);
        uintptr_t result3 = SearchByPattern(Patterns::Pattern3_bytes, Patterns::Pattern3_size);
        
        if (result1 == 0 && result2 == 0 && result3 == 0) {
            LogBypassOperation("No patterns found", 0, false);
        }
    }
    
    // 主要设置函数
    void SetupSimpleBypass() {
        if (!Config::ENABLE_SIMPLE_BYPASS) {
            OutputDebugStringA("[ClipSHOOK] Simple bypass disabled by configuration");
            return;
        }
        
        if (hookInstalled) {
            OutputDebugStringA("[ClipSHOOK] Simple bypass already installed");
            return;
        }
        
        OutputDebugStringA("[ClipSHOOK] Setting up simple bypass (NEW VERSION)...");
        
        // 测试模式：显示所有可能的地址
        if (Config::ENABLE_DEBUG_OUTPUT) {
            TestAllKnownAddresses();
            TestPatternSearch();
        }
        
        // 查找checkData地址
        uintptr_t checkDataAddr = FindCheckDataAddress();
        if (checkDataAddr == 0) {
            LogBypassOperation("Failed to find checkData address", 0, false);
            OutputDebugStringA("[ClipSHOOK] Simple bypass setup failed - no valid address found");
            return;
        }
        
        // 尝试Hook
        if (TryHookAtAddress(checkDataAddr)) {
            hookInstalled = true;
            OutputDebugStringA("[ClipSHOOK] Simple bypass setup completed successfully!");
        } else {
            OutputDebugStringA("[ClipSHOOK] Simple bypass setup failed - hook installation failed");
        }
    }
    
    void CleanupSimpleBypass() {
        hookInstalled = false;
        OutputDebugStringA("[ClipSHOOK] Simple bypass cleanup completed");
    }
}
