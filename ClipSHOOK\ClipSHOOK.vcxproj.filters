﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="源文件">
      <UniqueIdentifier>{4FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
      <Extensions>cpp;c;cc;cxx;c++;cppm;ixx;def;odl;idl;hpj;bat;asm;asmx</Extensions>
    </Filter>
    <Filter Include="头文件">
      <UniqueIdentifier>{93995380-89BD-4b04-88EB-625FBE52EBFB}</UniqueIdentifier>
      <Extensions>h;hh;hpp;hxx;h++;hm;inl;inc;ipp;xsd</Extensions>
    </Filter>
    <Filter Include="资源文件">
      <UniqueIdentifier>{67DA6AB6-F800-4c08-8B7A-83BB121AAD01}</UniqueIdentifier>
      <Extensions>rc;ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe;resx;tiff;tif;png;wav;mfcribbon-ms</Extensions>
    </Filter>
    <Filter Include="libudis86">
      <UniqueIdentifier>{B83C8C78-3F4A-4A8E-9F7D-8B5E2A1C9D3F}</UniqueIdentifier>
    </Filter>

  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="CSPHook.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="HookTool.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="ProjConfig.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="RegionBypass.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="LicenseBypass.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="libudis86\decode.h">
      <Filter>libudis86</Filter>
    </ClInclude>
    <ClInclude Include="libudis86\extern.h">
      <Filter>libudis86</Filter>
    </ClInclude>
    <ClInclude Include="libudis86\itab.h">
      <Filter>libudis86</Filter>
    </ClInclude>
    <ClInclude Include="libudis86\syn.h">
      <Filter>libudis86</Filter>
    </ClInclude>
    <ClInclude Include="libudis86\types.h">
      <Filter>libudis86</Filter>
    </ClInclude>
    <ClInclude Include="libudis86\udint.h">
      <Filter>libudis86</Filter>
    </ClInclude>
    <ClInclude Include="libudis86\udis86.h">
      <Filter>libudis86</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="dllmain.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="CSPHook.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="HookTool.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="LicenseBypass.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="libudis86\decode.c">
      <Filter>libudis86</Filter>
    </ClCompile>
    <ClCompile Include="libudis86\itab.c">
      <Filter>libudis86</Filter>
    </ClCompile>
    <ClCompile Include="libudis86\syn-att.c">
      <Filter>libudis86</Filter>
    </ClCompile>
    <ClCompile Include="libudis86\syn-intel.c">
      <Filter>libudis86</Filter>
    </ClCompile>
    <ClCompile Include="libudis86\syn.c">
      <Filter>libudis86</Filter>
    </ClCompile>
    <ClCompile Include="libudis86\udis86.c">
      <Filter>libudis86</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <None Include="..\README.md" />
  </ItemGroup>
</Project>