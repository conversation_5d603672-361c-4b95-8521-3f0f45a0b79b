#include "pch.h"
#include "SaveFileBypass.h"
#include "HookTool.h"
#include "CSPHook.h"
#include <cstdint>

namespace SaveFileBypass {

    // 定义特征码数组
    namespace Patterns {
        const uint8_t CodePart2_bytes[3] = { 0x84, 0xC0, 0x74 };
        const uint8_t CodePart3_bytes[4] = { 0x48, 0x85, 0xC0, 0x74 };
        const uint8_t MovRcxRax_bytes[3] = { 0x48, 0x8B, 0xC8 };
        const uint8_t TestEaxEax_bytes[2] = { 0x85, 0xC0 };
        const uint8_t SaveFile_bytes[21] = {
            0x48, 0x89, 0x9D, 0x38, 0x02, 0x00, 0x00, 0x0F, 0x10, 0x80,
            0x50, 0x01, 0x00, 0x00, 0x0F, 0x11, 0x85, 0x40, 0x02, 0x00, 0x00
        };
    }
    
    // 原始函数指针
    static bool(__fastcall* orig_CheckData)(void*, void*) = nullptr;
    
    // 工具函数实现
    void LogBypassOperation(const char* operation, bool success) {
        if (!Config::ENABLE_DEBUG_OUTPUT) return;
        
        char logMsg[256];
        sprintf_s(logMsg, "[ClipSHOOK] Save File Bypass - %s: %s", 
            operation, success ? "SUCCESS" : "FAILED");
        OutputDebugStringA(logMsg);
    }
    
    bool IsValidAddress(uintptr_t address) {
        __try {
            volatile uint8_t testByte = *(uint8_t*)address;
            (void)testByte;
            return true;
        }
        __except(EXCEPTION_EXECUTE_HANDLER) {
            return false;
        }
    }
    
    uint32_t CalculateRelativeAddress(uintptr_t from, uintptr_t to) {
        return (uint32_t)(to - from - 5); // -5 for call instruction size
    }
    
    bool IsCallToFunction(uint8_t* callPos, uintptr_t targetFunc, uintptr_t baseAddr) {
        if (*callPos != 0xE8) return false; // 不是call指令
        
        // 计算当前位置的虚拟地址
        uintptr_t currentAddr = baseAddr + ((uintptr_t)callPos - baseAddr);
        
        // 读取call指令的相对偏移
        uint32_t offset = *(uint32_t*)(callPos + 1);
        
        // 计算目标地址
        uintptr_t calculatedTarget = currentAddr + 5 + offset;
        
        return (calculatedTarget == targetFunc);
    }
    
    // 基于PE_PatchTool的匹配函数实现
    bool MatchCallCheckData(uint8_t* pos, uintptr_t baseAddr) {
        return IsCallToFunction(pos, Addresses::CheckData_Func, baseAddr);
    }
    
    bool MatchCallValueOf(uint8_t* pos, uintptr_t baseAddr) {
        return IsCallToFunction(pos, Addresses::ValueOf_Func, baseAddr);
    }
    
    bool MatchCallCheckLicense(uint8_t* pos, uintptr_t baseAddr) {
        return IsCallToFunction(pos, Addresses::CheckLicense_Func, baseAddr);
    }
    
    bool MatchCodePart2(uint8_t* pos) {
        // CODE PART 2: test al, al; je
        return memcmp(pos, Patterns::CodePart2_bytes, Patterns::CodePart2_size) == 0;
    }

    bool MatchCodePart3(uint8_t* pos) {
        // CODE PART 3: test rax, rax; je
        return memcmp(pos, Patterns::CodePart3_bytes, Patterns::CodePart3_size) == 0;
    }
    
    // 完整模式匹配（基于PE_PatchTool的MatchContent函数）
    bool MatchCompletePattern(uint8_t* pos, uintptr_t baseAddr) {
        uint8_t* originalPos = pos;
        
        // 匹配checkData调用
        if (!MatchCallCheckData(pos, baseAddr)) return false;
        pos += 5;
        
        // 匹配CODE PART 2
        if (!MatchCodePart2(pos)) return false;
        pos += 4; // 跳过 test al,al; je xx
        
        // 匹配第一个valueOf调用
        if (!MatchCallValueOf(pos, baseAddr)) return false;
        pos += 5;
        
        // 匹配CODE PART 3
        if (!MatchCodePart3(pos)) return false;
        pos += 5; // 跳过 test rax,rax; je xx
        
        // 匹配第二个valueOf调用
        if (!MatchCallValueOf(pos, baseAddr)) return false;
        pos += 5;
        
        // 匹配 mov rcx, rax
        if (memcmp(pos, Patterns::MovRcxRax_bytes, Patterns::MovRcxRax_size) != 0)
            return false;
        pos += 3;

        // 匹配正版检测函数调用
        if (!MatchCallCheckLicense(pos, baseAddr)) return false;
        pos += 5;

        // 匹配 test eax, eax
        if (memcmp(pos, Patterns::TestEaxEax_bytes, Patterns::TestEaxEax_size) != 0)
            return false;
        
        return true;
    }
    
    // 应用补丁（基于PE_PatchTool的PatchCode函数）
    void ApplyLicenseBypassPatch(uint8_t* pos) {
        LogBypassOperation("Applying license bypass patch at found pattern");
        
        // 应用短跳转
        *pos = 0xEB;        // jmp short
        *(pos + 1) = 0x19;  // 跳转偏移 0x19
        
        // 填充NOP指令
        for (int i = 0; i < 0x19; i++) {
            uint8_t* curPos = pos + 2 + i;
            *curPos = 0x90; // NOP
        }
        
        // 在test eax,eax之前插入mov eax,1
        uint8_t* curPos = pos + 0x1B;
        uint8_t op_mov_eax_1[5] = { 0xB8, 0x01, 0x00, 0x00, 0x00 }; // mov eax, 1
        memcpy(curPos, op_mov_eax_1, sizeof(op_mov_eax_1));
        
        LogBypassOperation("License bypass patch applied successfully");
    }
    
    // 搜索并应用保存文件补丁
    bool SearchAndPatchSaveRestriction() {
        uintptr_t baseAddr = CatHook::baseAddr;
        if (baseAddr == 0) {
            LogBypassOperation("Invalid base address", false);
            return false;
        }
        
        LogBypassOperation("Searching for save file restriction pattern...");
        
        // 搜索保存文件限制的特征码
        if (Config::USE_FIXED_ADDRESS) {
            // 使用固定地址（来自注释）
            uintptr_t patchAddr = baseAddr + 0x14049CC80;
            if (IsValidAddress(patchAddr)) {
                // 验证特征码
                uint8_t* pos = (uint8_t*)patchAddr;
                if (memcmp(pos, Patterns::SaveFile_bytes, Patterns::SaveFile_size) == 0) {
                    // 应用跳转补丁
                    uint8_t op_JMP[5] = { 0xE9, 0x83, 0x00, 0x00, 0x00 }; // jmp 14049CD08
                    CatHook::CodePatch((void*)patchAddr, op_JMP, sizeof(op_JMP));
                    LogBypassOperation("Fixed address save file patch applied");
                    return true;
                }
            }
        }
        
        if (Config::USE_PATTERN_SEARCH) {
            // 搜索完整的许可证检查模式
            for (uintptr_t offset = Config::SEARCH_START_OFFSET; 
                 offset < Config::SEARCH_END_OFFSET; 
                 offset += 0x10) {
                
                uintptr_t searchAddr = baseAddr + offset;
                if (!IsValidAddress(searchAddr)) continue;
                
                uint8_t* pos = (uint8_t*)searchAddr;
                if (MatchCompletePattern(pos, baseAddr)) {
                    LogBypassOperation("Found complete license check pattern");
                    ApplyLicenseBypassPatch(pos);
                    return true;
                }
            }
        }
        
        LogBypassOperation("Save file restriction pattern not found", false);
        return false;
    }
    
    // checkData Hook实现
    bool __fastcall Hook_CheckData(void* p1, void* p2) {
        // 强制返回true，绕过所有checkData验证
        if (Config::ENABLE_DEBUG_OUTPUT) {
            OutputDebugStringA("[ClipSHOOK] checkData called - returning TRUE (save bypass)");
        }
        return true;
    }
    
    void SetupCheckDataHook() {
        if (!Config::USE_CHECKDATA_HOOK) return;
        
        uintptr_t baseAddr = CatHook::baseAddr;
        uintptr_t checkDataAddr = baseAddr + (Addresses::CheckData_Func - 0x140000000);
        
        if (IsValidAddress(checkDataAddr)) {
            __try {
                CatHook::AutoHook((void*)checkDataAddr, (void*)Hook_CheckData, (void**)&orig_CheckData);
                LogBypassOperation("checkData Hook installed");
            }
            __except(EXCEPTION_EXECUTE_HANDLER) {
                LogBypassOperation("checkData Hook failed", false);
            }
        } else {
            LogBypassOperation("checkData address not valid", false);
        }
    }
    
    // 主要设置函数
    void SetupSaveFileBypass() {
        if (!Config::ENABLE_SAVE_FILE_BYPASS) {
            OutputDebugStringA("[ClipSHOOK] Save file bypass disabled by configuration");
            return;
        }
        
        OutputDebugStringA("[ClipSHOOK] Setting up save file bypass...");
        
        bool success = false;
        
        // 方法1：搜索并应用代码补丁
        if (SearchAndPatchSaveRestriction()) {
            success = true;
        }
        
        // 方法2：Hook checkData函数
        SetupCheckDataHook();
        
        if (success) {
            OutputDebugStringA("[ClipSHOOK] Save file bypass setup completed successfully!");
        } else {
            OutputDebugStringA("[ClipSHOOK] Save file bypass setup completed with limited success");
        }
    }
    
    void CleanupSaveFileBypass() {
        OutputDebugStringA("[ClipSHOOK] Save file bypass cleanup completed");
    }
}
