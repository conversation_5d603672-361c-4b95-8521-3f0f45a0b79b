#include "pch.h"
#include "LicenseBypass.h"
#include "HookTool.h"
#include "CSPHook.h"

namespace LicenseBypass {
    
    // 原始函数指针
    static bool(__fastcall* orig_CheckData)(void*, void*) = nullptr;
    static int(__fastcall* orig_CheckLicense)() = nullptr;
    static uintptr_t(__fastcall* orig_ValueOf)(uintptr_t) = nullptr;
    static void(__fastcall* orig_SkipSelectWindow)() = nullptr;
    
    // 工具函数实现
    void LogBypassOperation(const char* operation, bool success) {
        if (!Config::ENABLE_DEBUG_OUTPUT) return;
        
        char logMsg[256];
        sprintf_s(logMsg, "[ClipSHOOK] License Bypass - %s: %s", 
            operation, success ? "SUCCESS" : "FAILED");
        OutputDebugStringA(logMsg);
    }
    
    bool IsValidCSPVersion() {
        // 简单检查基址是否有效
        uintptr_t baseAddr = CatHook::baseAddr;
        return (baseAddr != 0 && baseAddr > 0x140000000);
    }
    
    // Hook函数实现
    bool __fastcall Hook_CheckData(void* p1, void* p2) {
        // 强制返回true，绕过所有checkData验证
        if (Config::ENABLE_DEBUG_OUTPUT) {
            OutputDebugStringA("[ClipSHOOK] checkData called - returning TRUE (bypassed)");
        }
        return true;
    }
    
    int __fastcall Hook_CheckLicense() {
        // 强制返回1，表示正版验证通过
        if (Config::ENABLE_DEBUG_OUTPUT) {
            OutputDebugStringA("[ClipSHOOK] License check called - returning 1 (bypassed)");
        }
        return 1;
    }
    
    uintptr_t __fastcall Hook_ValueOf(uintptr_t param) {
        // 返回一个有效的值，表示许可证有效
        if (Config::ENABLE_DEBUG_OUTPUT) {
            char logMsg[128];
            sprintf_s(logMsg, "[ClipSHOOK] valueOf(0x%llX) called - returning valid value", param);
            OutputDebugStringA(logMsg);
        }
        // 返回一个非零值表示有效
        return 0x1450209B8;  // 返回原始参数地址，表示有效
    }
    
    void __fastcall Hook_SkipSelectWindow() {
        // 跳过版本选择窗口的逻辑
        if (Config::ENABLE_DEBUG_OUTPUT) {
            OutputDebugStringA("[ClipSHOOK] Skip select window called - bypassed");
        }
        // 不执行原始函数，直接返回
        return;
    }
    
    // 代码补丁功能实现
    void ApplySaveFileBypass() {
        if (!IsValidCSPVersion()) return;
        
        uintptr_t baseAddr = CatHook::baseAddr;
        
        // 保存文件限制绕过（注释中的代码）
        // 0x14049CC80处改为 jmp 14049CD08
        // E9 83 00 00 00
        uint8_t op_JMP_14049CD08[5] = { 0xE9, 0x83, 0x00, 0x00, 0x00 };
        
        // 检查特征码
        uint8_t expected_pattern[] = { 0x48, 0x89, 0x9D, 0x38, 0x02, 0x00, 0x00, 0x0F, 0x10, 0x80, 0x50, 0x01, 0x00, 0x00, 0x0F, 0x11, 0x85, 0x40, 0x02, 0x00, 0x00 };
        
        void* patchAddr = (void*)(baseAddr + Addresses::SaveFile_Patch);
        
        // 应用补丁
        CatHook::CodePatch(patchAddr, op_JMP_14049CD08, sizeof(op_JMP_14049CD08));
        LogBypassOperation("Save file bypass patch applied");
    }
    
    void ApplySelectWindowBypass() {
        if (!IsValidCSPVersion()) return;
        
        uintptr_t baseAddr = CatHook::baseAddr;
        
        // 跳过初始选择版本的窗口
        // 0x1402B2C18处改为 call 0x1434DA340
        uint8_t op_Call_SkipSelectWindow[5] = { 0xE8, 0x23, 0x77, 0x22, 0x03 };
        
        void* patchAddr = (void*)(baseAddr + Addresses::SelectWindow_Patch);
        CatHook::CodePatch(patchAddr, op_Call_SkipSelectWindow, sizeof(op_Call_SkipSelectWindow));
        LogBypassOperation("Select window bypass patch applied");
    }
    
    void ApplyLicenseBypass() {
        if (!IsValidCSPVersion()) return;
        
        uintptr_t baseAddr = CatHook::baseAddr;
        
        // 正版验证绕过
        // 让1434dddd0处的函数返回1
        uint8_t op_mov_eax_1_ret[10] = { 
            0xb8, 0x01, 0x00, 0x00, 0x00,  // mov eax,1
            0xc3, 0x90, 0x90, 0x90, 0x90   // ret nop nop nop nop
        };
        
        void* licenseAddr = (void*)(baseAddr + Addresses::CheckLicense_Func);
        CatHook::CodePatch(licenseAddr, op_mov_eax_1_ret, sizeof(op_mov_eax_1_ret));
        
        // 跳过特殊的返回1会导致无法启动的地方
        uint8_t op_jmp_14032A0D6[2] = { 0XEB, 0X6D };
        void* jump1Addr = (void*)(baseAddr + Addresses::Jump_14032A067);
        CatHook::CodePatch(jump1Addr, op_jmp_14032A0D6, sizeof(op_jmp_14032A0D6));
        
        uint8_t op_jmp_14032A1AB_nop[6] = { 0XE9, 0X8B, 0X00, 0X00, 0X00, 0X90 };
        void* jump2Addr = (void*)(baseAddr + Addresses::Jump_14032A11B);
        CatHook::CodePatch(jump2Addr, op_jmp_14032A1AB_nop, sizeof(op_jmp_14032A1AB_nop));
        
        LogBypassOperation("License bypass patches applied");
    }
    
    void ApplyCodePatches() {
        if (!Config::USE_PATCH_METHOD) return;
        
        OutputDebugStringA("[ClipSHOOK] Applying license bypass code patches...");
        
        ApplySaveFileBypass();
        ApplySelectWindowBypass();
        ApplyLicenseBypass();
        
        OutputDebugStringA("[ClipSHOOK] License bypass code patches applied successfully!");
    }
    
    // 设置Hook
    void SetupLicenseBypass() {
        if (!Config::ENABLE_LICENSE_BYPASS) {
            OutputDebugStringA("[ClipSHOOK] License bypass disabled by configuration");
            return;
        }
        
        if (!IsValidCSPVersion()) {
            OutputDebugStringA("[ClipSHOOK] Invalid CSP version, license bypass skipped");
            return;
        }
        
        OutputDebugStringA("[ClipSHOOK] Installing license bypass hooks...");
        
        uintptr_t baseAddr = CatHook::baseAddr;
        
        // Hook方式
        if (Config::USE_HOOK_METHOD) {
            if (Config::HOOK_CheckData) {
                void* checkDataAddr = (void*)(baseAddr + Addresses::CheckData_Func);
                CatHook::AutoHook(checkDataAddr, (void*)Hook_CheckData, (void**)&orig_CheckData);
                LogBypassOperation("checkData Hook installed");
            }
            
            if (Config::HOOK_CheckLicense) {
                void* checkLicenseAddr = (void*)(baseAddr + Addresses::CheckLicense_Func);
                CatHook::AutoHook(checkLicenseAddr, (void*)Hook_CheckLicense, (void**)&orig_CheckLicense);
                LogBypassOperation("CheckLicense Hook installed");
            }
            
            if (Config::HOOK_ValueOf) {
                void* valueOfAddr = (void*)(baseAddr + Addresses::ValueOf_Func);
                CatHook::AutoHook(valueOfAddr, (void*)Hook_ValueOf, (void**)&orig_ValueOf);
                LogBypassOperation("valueOf Hook installed");
            }
            
            if (Config::HOOK_SkipSelectWindow) {
                void* skipWindowAddr = (void*)(baseAddr + Addresses::SkipSelectWindow_Func);
                CatHook::AutoHook(skipWindowAddr, (void*)Hook_SkipSelectWindow, (void**)&orig_SkipSelectWindow);
                LogBypassOperation("SkipSelectWindow Hook installed");
            }
        }
        
        // 代码补丁方式
        if (Config::USE_PATCH_METHOD) {
            ApplyCodePatches();
        }
        
        OutputDebugStringA("[ClipSHOOK] License bypass setup completed successfully!");
    }
    
    void CleanupLicenseBypass() {
        OutputDebugStringA("[ClipSHOOK] License bypass cleanup completed");
    }
}
