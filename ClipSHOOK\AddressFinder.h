#pragma once

//=============================================================================
// ClipSHOOK 地址查找工具
// 用于帮助用户找到正确的CSP函数地址
//=============================================================================

#include <windows.h>

namespace AddressFinder {
    
    // 配置选项
    namespace Config {
        // 是否启用地址查找功能
        constexpr bool ENABLE_ADDRESS_FINDER = true;
        
        // 是否启用调试输出
        constexpr bool ENABLE_DEBUG_OUTPUT = true;
        
        // 搜索范围配置
        constexpr size_t SEARCH_START_OFFSET = 0x1000;      // 搜索起始偏移
        constexpr size_t SEARCH_END_OFFSET = 0x5000000;     // 搜索结束偏移
        constexpr size_t SEARCH_STEP = 0x10;                // 搜索步长
    }
    
    // 特征码定义
    namespace Patterns {
        // checkData函数特征码
        struct CheckDataPattern {
            static constexpr uint8_t bytes[] = { 0x48, 0x89, 0x5C, 0x24, 0x08, 0x57, 0x48, 0x83, 0xEC, 0x20 };
            static constexpr size_t size = sizeof(bytes);
            static constexpr const char* name = "checkData";
        };
        
        // 正版验证函数特征码
        struct LicenseCheckPattern {
            static constexpr uint8_t bytes[] = { 0x40, 0x53, 0x48, 0x83, 0xEC, 0x20, 0x48, 0x8B, 0xD9 };
            static constexpr size_t size = sizeof(bytes);
            static constexpr const char* name = "LicenseCheck";
        };
        
        // valueOf函数特征码
        struct ValueOfPattern {
            static constexpr uint8_t bytes[] = { 0x48, 0x89, 0x5C, 0x24, 0x10, 0x48, 0x89, 0x74, 0x24, 0x18 };
            static constexpr size_t size = sizeof(bytes);
            static constexpr const char* name = "valueOf";
        };
    }
    
    // 地址查找功能
    void FindAllAddresses();
    void FindCheckDataAddress();
    void FindLicenseCheckAddress();
    void FindValueOfAddress();
    
    // 工具函数
    bool SearchPattern(const uint8_t* pattern, size_t patternSize, const char* name);
    bool IsValidAddress(uintptr_t address);
    void LogFoundAddress(const char* functionName, uintptr_t address);
    
    // 内存搜索函数
    uintptr_t SearchMemoryPattern(uintptr_t startAddr, size_t searchSize, 
        const uint8_t* pattern, size_t patternSize);
    
    // 验证函数
    bool VerifyCheckDataFunction(uintptr_t address);
    bool VerifyLicenseCheckFunction(uintptr_t address);
    bool VerifyValueOfFunction(uintptr_t address);
}

//=============================================================================
// 使用说明
//=============================================================================
/*
地址查找工具使用说明：

1. 自动搜索：
   - 启用ENABLE_ADDRESS_FINDER = true
   - ClipSHOOK会在启动时自动搜索所有函数地址
   - 结果会输出到DebugView

2. 手动验证：
   - 使用反汇编工具（如x64dbg）打开CSP
   - 搜索特征码来验证地址
   - 更新LicenseBypass.h中的地址

3. 特征码说明：
   - checkData: 48 89 5C 24 08 57 48 83 EC 20
   - LicenseCheck: 40 53 48 83 EC 20 48 8B D9
   - valueOf: 48 89 5C 24 10 48 89 74 24 18

4. 地址格式：
   - 输出格式：[ClipSHOOK] Found checkData at: 0x143567B10
   - 使用时需要减去基址：0x143567B10 - 0x140000000 = 0x3567B10

注意事项：
- 不同版本的CSP地址可能不同
- 特征码可能因版本而变化
- 建议在测试环境中验证地址
- 错误的地址可能导致CSP崩溃
*/
