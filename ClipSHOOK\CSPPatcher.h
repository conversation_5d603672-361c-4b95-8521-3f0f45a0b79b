#pragma once

//=============================================================================
// ClipSHOOK CSP补丁器
// 基于您提供的完整PE_PatchTool代码实现
//=============================================================================

#include <windows.h>

namespace CSPPatcher {
    
    // 配置选项
    namespace Config {
        // 是否启用CSP补丁器
        constexpr bool ENABLE_CSP_PATCHER = true;

        // 是否启用调试输出
        constexpr bool ENABLE_DEBUG_OUTPUT = true;

        // 是否使用特征码搜索（推荐）
        constexpr bool USE_PATTERN_SEARCH = true;

        // 是否使用固定地址（您的CSP版本地址不匹配，暂时禁用）
        constexpr bool USE_FIXED_ADDRESSES = false;

        // 搜索范围
        constexpr size_t SEARCH_START = 0x1000;
        constexpr size_t SEARCH_END = 0x8000000;  // 基于您的代码段大小

        // 专注于保存文件绕过
        constexpr bool FOCUS_ON_SAVE_FILE = true;
    }
    
    // 地址定义（基于您的注释）
    namespace Addresses {
        constexpr uintptr_t CheckData_Func = 0x143567B10;
        constexpr uintptr_t ValueOf_Func = 0x1434da5B0;
        constexpr uintptr_t CheckLicense_Func = 0x1434DDDD0;
        
        constexpr uintptr_t SaveFile_Patch = 0x14049CC80;
        constexpr uintptr_t SelectWindow_Patch = 0x1402B2C18;
        constexpr uintptr_t TrialString_Patch = 0x1403D00FC;
        constexpr uintptr_t Plugin_Patch = 0x1402D446B;
        constexpr uintptr_t License_Patch = 0x1434dddd0;
        constexpr uintptr_t Jump1_Patch = 0x14032A067;
        constexpr uintptr_t Jump2_Patch = 0x14032A11B;
    }
    
    // 主要功能函数
    void SetupCSPPatches();
    void CleanupCSPPatches();
    
    // 基于您PE_PatchTool的匹配函数
    bool MatchCallValueOf1450209b8(uint8_t* pos, uint8_t* posStart, uintptr_t baseAddr);
    bool MatchCallCheckData(uint8_t* pos, uint8_t* posStart, uintptr_t baseAddr);
    bool MatchCallCheckLicense(uint8_t* pos, uint8_t* posStart, uintptr_t baseAddr);
    bool MatchCodePart2(uint8_t* pos);
    bool MatchCodePart3(uint8_t* pos);
    bool MatchContent(uint8_t* pos, uint8_t* posStart, uintptr_t baseAddr);
    
    // 补丁应用函数
    void PatchCode(uint8_t* pos);
    void ApplySaveFileBypass();
    void ApplySelectWindowBypass();
    void ApplyTrialStringBypass();
    void ApplyPluginBypass();
    void ApplyLicenseBypass();
    
    // 特征码搜索
    uint8_t* SearchLicenseCheckPattern();
    
    // 工具函数
    bool IsValidAddress(uintptr_t address);
    void LogPatchOperation(const char* operation, uintptr_t address = 0, bool success = true);
}

//=============================================================================
// 实现说明
//=============================================================================
/*
CSP补丁器实现说明：

基于您提供的完整解决方案，包含以下功能：

1. 保存文件绕过：
   - 特征码：48 89 9D 38 02 00 00 0F 10 80 50 01 00 00 0F 11 85 40 02 00 00
   - 补丁：0x14049CC80处改为 jmp 14049CD08 (E9 83 00 00 00)

2. 跳过初始选择版本窗口：
   - 特征码：4C 8D 9C 24 B0 03 00 00 49 8B 5B 38 49 8B 73 40 49 8B E3 41 5F 41 5E 41 5D 41 5C 5F C3
   - 补丁：0x1402B2C18处改为 call 0x1434DA340 (E8 23 77 22 03)

3. 隐藏体验版文字：
   - 特征码：49 8B 87 18 03 00 00 48 89 45 C0 49 8B 87 20 03 00 00 48 89 45 C8 48 85 C0 74 06 F0 44 0F C1 70 08 48 8D 95 48 03 00 00 48 8D 4D C0
   - 补丁：0x1403D00FC处改为 jmp (EB 57)

4. 解锁插件功能：
   - 特征码：90 0F 57 C0 F3 0F 7F 45 D8 4C 89 6D E8 F3 0F 7F 45 F0 4C 89 6D 00 F3 0F 7F 45 08 4C 89 6D 18 F3 0F 7F 45 20 4C 89 6D 30
   - 补丁：0x1402D446B处改为 jmp (e9 9b 00 00 00)

5. 正版验证绕过：
   - 让1434dddd0处的函数返回1
   - 跳过特殊的返回1会导致无法启动的地方

6. 完整的许可证检查模式匹配：
   - 基于您PE_PatchTool的MatchContent函数
   - 匹配完整的验证序列并应用补丁

这个实现完全基于您提供的工作代码和地址信息。
*/
