#pragma once

//=============================================================================
// ClipSHOOK 文件保存Hook功能
// 用于监控和修改CSP的文件保存行为
//=============================================================================

#include <windows.h>

namespace FileSaveHook {
    
    // 配置选项
    namespace Config {
        // 是否启用文件保存Hook
        constexpr bool ENABLE_FILE_SAVE_HOOK = true;
        
        // 是否启用调试输出
        constexpr bool ENABLE_DEBUG_OUTPUT = true;
        
        // 是否记录所有文件操作
        constexpr bool LOG_ALL_FILE_OPERATIONS = false;
        
        // 是否只记录CSP相关文件
        constexpr bool LOG_CSP_FILES_ONLY = true;
        
        // Hook的API列表
        constexpr bool HOOK_CreateFileW = true;
        constexpr bool HOOK_WriteFile = true;
        constexpr bool HOOK_CloseHandle = true;
        constexpr bool HOOK_SetFilePointer = false;  // 可选
        constexpr bool HOOK_GetFileSize = false;     // 可选
    }
    
    // 文件类型识别
    namespace FileTypes {
        // CSP项目文件扩展名
        constexpr const wchar_t* CSP_PROJECT_EXT[] = {
            L".clip", L".csp", L".lip", L".psd", L".psb", 
            L".png", L".jpg", L".jpeg", L".bmp", L".tiff", L".tga",
            nullptr
        };
        
        // 需要特别关注的文件路径关键词
        constexpr const wchar_t* CSP_PATH_KEYWORDS[] = {
            L"CELSYS", L"CLIP STUDIO", L"ClipStudio", L"Documents",
            L"Pictures", L"Desktop", L"Downloads",
            nullptr
        };
    }
    
    // Hook函数声明
    void SetupFileSaveHook();
    void CleanupFileSaveHook();
    
    // 工具函数
    bool IsCSPRelatedFile(const wchar_t* filePath);
    bool IsImageFile(const wchar_t* filePath);
    bool IsProjectFile(const wchar_t* filePath);
    void LogFileOperation(const wchar_t* operation, const wchar_t* filePath, DWORD result = 0);
    
    // Hook函数实现
    HANDLE WINAPI Hook_CreateFileW(
        LPCWSTR lpFileName,
        DWORD dwDesiredAccess,
        DWORD dwShareMode,
        LPSECURITY_ATTRIBUTES lpSecurityAttributes,
        DWORD dwCreationDisposition,
        DWORD dwFlagsAndAttributes,
        HANDLE hTemplateFile
    );
    
    BOOL WINAPI Hook_WriteFile(
        HANDLE hFile,
        LPCVOID lpBuffer,
        DWORD nNumberOfBytesToWrite,
        LPDWORD lpNumberOfBytesWritten,
        LPOVERLAPPED lpOverlapped
    );
    
    BOOL WINAPI Hook_CloseHandle(
        HANDLE hObject
    );
}

//=============================================================================
// 使用示例和说明
//=============================================================================
/*
文件保存Hook功能说明：

1. 监控文件创建：
   - Hook CreateFileW API
   - 记录CSP创建/打开的所有文件
   - 识别保存操作（GENERIC_WRITE访问）

2. 监控文件写入：
   - Hook WriteFile API
   - 记录写入的数据量
   - 可以修改写入内容（高级功能）

3. 监控文件关闭：
   - Hook CloseHandle API
   - 确认文件保存完成
   - 记录保存操作的完整生命周期

4. 文件类型识别：
   - 自动识别CSP项目文件
   - 区分图像文件和项目文件
   - 过滤系统文件和临时文件

5. 调试和日志：
   - 详细的文件操作日志
   - 可配置的输出级别
   - 性能影响最小化

配置选项：
- ENABLE_FILE_SAVE_HOOK: 总开关
- LOG_ALL_FILE_OPERATIONS: 记录所有文件操作
- LOG_CSP_FILES_ONLY: 只记录CSP相关文件

高级功能（可扩展）：
- 自动备份保存的文件
- 修改保存路径
- 压缩保存的文件
- 加密敏感文件
- 同步到云存储

注意事项：
- Hook文件API可能影响性能
- 需要谨慎处理大文件操作
- 避免Hook系统关键文件
- 确保不影响CSP正常功能
*/
