// pch.cpp: 与预编译标头对应的源文件
#include"../HookTool.h"

#include "CspData.h"




void CspData::GetCurrentProjCanvasSize(uint32_t* sizeW, uint32_t* sizeH)
{
//144f61560 + 290 + 20 + 38 +2b8
	uintptr_t pSize = *(uintptr_t*)(CatHook::baseAddr + 0x144f61560);
	pSize = *(uintptr_t*)(pSize+0x290);
	pSize = *(uintptr_t*)(pSize+0x20);
	pSize = *(uintptr_t*)(pSize+0x38);
	pSize = pSize+0x2b8;
	*sizeW = *(uint32_t*)pSize;
	*sizeH = *(uint32_t*)(pSize+4);
}




uintptr_t CspData::GetNativeWindowHandle()
{
	//windows
	//0X144F661A8
	//直接搜索窗口句柄就能搜索到基址
	//这是主窗口地址，不一定是最前台窗口的地址
	return *(uintptr_t*)(0X144F661A8);
}




// UI颜色管理功能已移除




void CspData::SetUpHook()
{
	// UI相关Hook已移除
}


 


uintptr_t CspColorTable::GetColorTableAddr()
{
	//0x144f62860 + 8 +0
	uintptr_t pColorTable = *(uintptr_t*)(CatHook::baseAddr + 0x144f62860);
	pColorTable = *(uintptr_t*)(pColorTable + 8);
	return pColorTable;
}

uint32_t CspColorTable::_GetColorAt(uintptr_t offset)
{
	uintptr_t pColorTable = GetColorTableAddr();
	return *(uint32_t*)(pColorTable + offset);
}

uint32_t CspColorTable::GetColorCaptionNoFocus()
{
	return _GetColorAt(0X288);
}

uint32_t CspColorTable::GetColorCaptionFocus()
{
	return _GetColorAt(0X24);
}

uint32_t CspColorTable::GetColorPanelBG()
{
	return _GetColorAt(0x48);
}

uint32_t CspColorTable::GetColorControlBG()
{
	return _GetColorAt(0x144);
}

uint32_t CspColorTable::GetContentColor()
{
	return _GetColorAt(0x120);
}

uint32_t CspColorTable::GetSeparatorColor()
{
	return _GetColorAt(0x264);
}

uint32_t CspColorTable::GetCaptionContentColor()
{
	return _GetColorAt(0x18);
}

uint32_t CspColorTable::GetCaptionContentColorNoFocus()
{
	return _GetColorAt(0x280);
}

uint32_t CspColorTable::GetSelectedColor()
{
	return _GetColorAt(0x1e0);
}

uint32_t CspColorTable::GetSelectedHotColor()
{
	return _GetColorAt(0x1e4);
}

uint32_t CspColorTable::GetBlueButtonColor()
{
	return _GetColorAt(0x1c8);
}

uint32_t CspColorTable::GetBlueButtonHotColor()
{
	return _GetColorAt(0x1cc);
}

uint32_t CspColorTable::GetBlueButtonPushColor()
{
	return _GetColorAt(0x1d0);
}

uint32_t CspColorTable::GetButtonPushColor()
{
	return _GetColorAt(0x1d8);
}
