# UDIS86 库配置说明

## 概述
AutoHook功能需要udis86反汇编库来计算指令长度。

## ✅ 当前状态：已集成
udis86库已经直接集成到ClipSHOOK项目中，无需额外配置！

## 获取udis86库

### 方法1：从官方源码编译
1. 下载udis86源码：https://github.com/vmt/udis86
2. 使用Visual Studio编译生成静态库

### 方法2：从duilib项目提取
1. 下载nim_duilib项目：https://github.com/rhett-lee/nim_duilib
2. 提取 `duilib/third_party/libudis86/` 文件夹
3. 编译生成静态库

## 配置步骤

### 1. 创建udis86目录结构
在项目根目录创建以下结构：
```
ClipSHOOK/
├── udis86/
│   ├── include/
│   │   └── udis86.h
│   └── lib/
│       ├── x64/
│       │   └── udis86.lib
│       └── x86/
│           └── udis86.lib
```

### 2. 更新项目配置
在 `ClipSHOOK.vcxproj` 中添加以下配置：

#### 添加包含目录
```xml
<PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
  <IncludePath>$(ProjectDir)udis86\include;$(IncludePath)</IncludePath>
</PropertyGroup>
<PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
  <IncludePath>$(ProjectDir)udis86\include;$(IncludePath)</IncludePath>
</PropertyGroup>
```

#### 添加库目录和依赖
```xml
<ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
  <Link>
    <AdditionalLibraryDirectories>$(ProjectDir)udis86\lib\x64;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
    <AdditionalDependencies>udis86.lib;%(AdditionalDependencies)</AdditionalDependencies>
  </Link>
</ItemDefinitionGroup>
```

### 3. 验证配置
编译项目，确保：
1. 能找到 `udis86.h` 头文件
2. 能链接到 `udis86.lib` 库文件
3. AutoHook功能正常工作

## 注意事项
1. 确保udis86库的架构（x86/x64）与项目配置匹配
2. 确保udis86库的运行时库配置与项目一致（MT/MD）
3. 如果遇到链接错误，检查库文件路径和依赖配置

## 替代方案
如果不想使用udis86库，可以：
1. 使用固定的指令长度（如12字节）进行Hook
2. 手动分析目标函数的指令长度
3. 使用其他反汇编库（如Zydis、Capstone等）

## 当前状态
- ✅ HookTool.cpp 已恢复完整的AutoHook实现
- ✅ udis86库已直接集成到项目中
- ✅ AutoHook功能可以直接使用
- ✅ 其他Hook功能（Hook、HookEx）正常工作
- ✅ 项目可以直接编译，无需额外配置
